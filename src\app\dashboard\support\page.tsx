"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

// Mock data for chat history
const initialMessages = [
  {
    id: 1,
    sender: "support",
    message: "Hello! Welcome to Kredxa Support. How can I help you today?",
    timestamp: "2024-03-20T10:00:00",
  },
  {
    id: 2,
    sender: "user",
    message: "Hi, I'm having trouble with my loan application. It's been pending for 3 days now.",
    timestamp: "2024-03-20T10:01:00",
  },
  {
    id: 3,
    sender: "support",
    message: "I understand your concern. Could you please share your loan application ID? This will help me check the status for you.",
    timestamp: "2024-03-20T10:02:00",
  },
];

export default function SupportPage() {
  const [messages, setMessages] = useState(initialMessages);
  const [newMessage, setNewMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      sender: "user",
      message: newMessage,
      timestamp: new Date().toISOString(),
    };
    setMessages([...messages, userMessage]);
    setNewMessage("");
    setIsTyping(true);

    // Simulate support response after 2 seconds
    setTimeout(() => {
      const supportMessage = {
        id: messages.length + 2,
        sender: "support",
        message: "Thank you for your message. Our support team will get back to you shortly. In the meantime, you can check our FAQ section for quick answers to common questions.",
        timestamp: new Date().toISOString(),
      };
      setMessages(prev => [...prev, supportMessage]);
      setIsTyping(false);
    }, 2000);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Support</h1>
        <p className="text-gray-500 text-sm mt-1">
          Chat with our support team for assistance
        </p>
      </div>

      {/* Chat Interface */}
      <Card className="h-[600px] flex flex-col">
        <CardContent className="flex-1 flex flex-col p-4">
          {/* Chat Header */}
          <div className="border-b pb-4 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">Support Team Online</span>
            </div>
          </div>

          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto space-y-4 mb-4">
            {messages.map((msg) => (
              <div
                key={msg.id}
                className={`flex ${msg.sender === "user" ? "justify-end" : "justify-start"}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    msg.sender === "user"
                      ? "bg-indigo-600 text-white"
                      : "bg-gray-100 text-gray-900"
                  }`}
                >
                  <p className="text-sm">{msg.message}</p>
                  <span
                    className={`text-xs mt-1 block ${
                      msg.sender === "user" ? "text-indigo-200" : "text-gray-500"
                    }`}
                  >
                    {formatTimestamp(msg.timestamp)}
                  </span>
                </div>
              </div>
            ))}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 rounded-lg p-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.4s" }}></div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Message Input */}
          <div className="border-t pt-4">
            <div className="flex gap-2">
              <Input
                placeholder="Type your message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                className="flex-1"
              />
              <Button onClick={handleSendMessage}>Send</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Links */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">FAQ</h3>
            <p className="text-sm text-gray-500">Find answers to common questions</p>
            <Button variant="link" className="p-0 h-auto mt-2">View FAQ →</Button>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Email Support</h3>
            <p className="text-sm text-gray-500"><EMAIL></p>
            <Button variant="link" className="p-0 h-auto mt-2">Send Email →</Button>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Phone Support</h3>
            <p className="text-sm text-gray-500">Available 24/7</p>
            <Button variant="link" className="p-0 h-auto mt-2">Call Now →</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 