"use client";
import React from 'react';
import Image from 'next/image';
import { Button } from './ui/button';
import { useRouter } from 'next/navigation';

const ReadyToStart = () => {
  const router = useRouter();

  const handleRedirect = () => {
    router.push('/404');
  };

  return (
    <div className="bg-white py-12 sm:py-16">
      <div className="max-w-4xl mx-auto flex flex-col md:flex-row items-center text-center md:text-left px-4">
        <div className="w-full md:w-1/2">
          <Image src="/image 15.svg" alt="Rocket" width={400} height={400} className="mx-auto" />
        </div>
        <div className="w-full md:w-1/2 mt-8 md:mt-0">
          <h3 className="text-base sm:text-lg font-semibold text-gray-600">Ready To Start</h3>
          <h2 className="text-3xl sm:text-4xl font-bold mt-2">Lending Or Borrowing</h2>
          <p className="text-gray-500 mt-4 text-sm sm:text-base">
            What are you waiting for? make things happen the way you want with Kredxa!
          </p>
          <Button
            className="mt-6 bg-black text-white rounded-full px-8 py-3"
            onClick={handleRedirect}
          >
            GET STARTED TODAY
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReadyToStart;