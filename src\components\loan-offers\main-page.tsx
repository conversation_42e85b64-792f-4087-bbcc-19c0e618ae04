import React from 'react';
import DefineLoanSection from './define-loan-section';
import ReviewAndConfirm from './review&confirm-section';
import Sidebar from '../dashboard/Sidebar';
import Header from '../dashboard/Header';

const MainPage = () => {
    return (
        <div className="flex min-h-screen bg-gray-50">
            <Sidebar />
            <div className="flex-1 flex flex-col">
                <Header />
                <main className="flex-1 p-6">
                    <div className='max-w-2xl mx-auto'>
                        <h1 className='text-2xl font-bold text-[#1a1a1a] mb-2'>Create a New Loan Offer</h1>
                        <p className='text-sm text-[#1a1a1a] mb-6'>Set the loan terms that borrowers will see on the marketplace. Eligibility and repayment settings are managed by Kredxa to ensure fairness and consistency.</p>
                        <DefineLoanSection />
                        <ReviewAndConfirm />
                    </div>
                </main>
            </div>
        </div>
    );
}

export default MainPage;
