"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

// Mock data for loans
const loans = [
  {
    id: "LN-001234",
    amount: "₦500,000",
    status: "active",
    lender: "First Bank",
    interestRate: "22% p.a.",
    term: "12 months",
    startDate: "2024-03-01",
    nextPayment: "2024-04-01",
    amountPaid: "₦83,333",
    totalAmount: "₦610,000",
    remainingAmount: "₦526,667",
    progress: 30,
  },
  {
    id: "LN-001235",
    amount: "₦300,000",
    status: "pending",
    lender: "GTBank",
    interestRate: "20% p.a.",
    term: "6 months",
    startDate: "2024-03-15",
    nextPayment: "2024-04-15",
    amountPaid: "₦0",
    totalAmount: "₦330,000",
    remainingAmount: "₦330,000",
    progress: 0,
  },
  {
    id: "LN-001236",
    amount: "₦1,000,000",
    status: "completed",
    lender: "Access Bank",
    interestRate: "18% p.a.",
    term: "24 months",
    startDate: "2023-03-01",
    nextPayment: "N/A",
    amountPaid: "₦1,180,000",
    totalAmount: "₦1,180,000",
    remainingAmount: "₦0",
    progress: 100,
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800";
    case "pending":
      return "bg-yellow-100 text-yellow-800";
    case "completed":
      return "bg-blue-100 text-blue-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function MyLoansPage() {
  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">My Loans</h1>
        <Button>Apply for New Loan</Button>
      </div>

      {/* Loan Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Active Loans</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-sm text-gray-500">Total amount: ₦800,000</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Next Payment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₦83,333</div>
            <p className="text-sm text-gray-500">Due in 12 days</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Repaid</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₦186,666</div>
            <p className="text-sm text-gray-500">Across all loans</p>
          </CardContent>
        </Card>
      </div>

      {/* Loans List */}
      <div className="space-y-4">
        {loans.map((loan) => (
          <Card key={loan.id}>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-2">
                    <h3 className="text-lg font-semibold">{loan.id}</h3>
                    <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(loan.status)}`}>
                      {loan.status.charAt(0).toUpperCase() + loan.status.slice(1)}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Amount</p>
                      <p className="font-medium">{loan.amount}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Lender</p>
                      <p className="font-medium">{loan.lender}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Interest Rate</p>
                      <p className="font-medium">{loan.interestRate}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Term</p>
                      <p className="font-medium">{loan.term}</p>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Progress</p>
                    <div className="w-32 h-2 bg-gray-200 rounded-full mt-1">
                      <div
                        className="h-full bg-green-500 rounded-full"
                        style={{ width: `${loan.progress}%` }}
                      />
                    </div>
                    <p className="text-sm font-medium mt-1">{loan.progress}%</p>
                  </div>
                  <Button variant="outline" className="w-full md:w-auto">
                    View Details
                  </Button>
                </div>
              </div>

              {/* Additional Details */}
              <div className="mt-4 pt-4 border-t grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Start Date</p>
                  <p className="font-medium">{loan.startDate}</p>
                </div>
                <div>
                  <p className="text-gray-500">Next Payment</p>
                  <p className="font-medium">{loan.nextPayment}</p>
                </div>
                <div>
                  <p className="text-gray-500">Amount Paid</p>
                  <p className="font-medium">{loan.amountPaid}</p>
                </div>
                <div>
                  <p className="text-gray-500">Remaining</p>
                  <p className="font-medium">{loan.remainingAmount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
} 