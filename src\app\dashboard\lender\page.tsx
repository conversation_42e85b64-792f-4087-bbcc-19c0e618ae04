import React from "react";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

const stats = [
  { label: "Total Disbursed", value: "₦6.4M", icon: "💳" },
  { label: "Active Loans", value: 8, icon: "📊" },
  { label: "Total Earnings", value: "₦1.3M", icon: "📁" },
  { label: "New Applications", value: 5, icon: "➕", link: "#", sub: "View All" },
];

const applications = [
  {
    name: "<PERSON>",
    amount: "₦700,000",
    type: "SME Growth Loan",
    status: "Under Review",
    avatar: "https://randomuser.me/api/portraits/men/45.jpg",
  },
  {
    name: "<PERSON><PERSON>o",
    amount: "₦200,000",
    type: "Personal Flexi Loan",
    status: "Under Review",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
  },
  {
    name: "<PERSON>",
    amount: "₦1,200,000",
    type: "Business Expansion",
    status: "New",
    avatar: "https://randomuser.me/api/portraits/men/46.jpg",
  },
];

const earnings = {
  month: "₦180,000",
  disbursed: "₦6.4M",
  repaid: "₦5.1M",
};

const portfolio = {
  performing: 7,
  overdue: 1,
  repayments: "₦5,100,000",
  percent: 80,
};

export default function LenderDashboard() {
  return (
    <div className="space-y-6">
      {/* Portfolio Overview & Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Portfolio Overview */}
        <div className="bg-white rounded-3xl shadow p-8 flex flex-col justify-between">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Portfolio Overview</h2>
            <a href="#" className="text-indigo-600 font-semibold hover:underline">View Portfolio</a>
          </div>
          <div className="flex gap-6 mb-6">
            <div className="flex-1 flex flex-col items-center justify-center bg-green-50 rounded-2xl border border-green-100 py-6">
              <div className="text-4xl font-bold text-green-600">{portfolio.performing}</div>
              <div className="text-lg text-green-600">Performing Loans</div>
            </div>
            <div className="flex-1 flex flex-col items-center justify-center bg-red-50 rounded-2xl border border-red-100 py-6">
              <div className="text-4xl font-bold text-red-600">{portfolio.overdue}</div>
              <div className="text-lg text-red-600">Overdue</div>
            </div>
          </div>
          <div className="mb-2 text-lg font-medium text-gray-700">Total Repayments <span className="float-right font-bold text-black">{portfolio.repayments}</span></div>
          <div className="w-full h-4 bg-gray-200 rounded-full mb-1">
            <div className="h-4 bg-green-500 rounded-full" style={{ width: `${portfolio.percent}%` }}></div>
          </div>
          <div className="text-sm text-gray-500 text-right">{portfolio.percent}% of total portfolio</div>
        </div>
        {/* Quick Actions */}
        <div className="bg-white rounded-3xl shadow p-8 flex flex-col justify-between">
          <h2 className="text-2xl font-bold mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Link href="/dashboard/lender/create-offer" className="flex flex-col items-center justify-center bg-blue-50 rounded-2xl border border-blue-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-blue-500 mb-2">+</span>
              <span className="font-medium text-lg">Create Loan Offer</span>
            </Link>
            <div className="flex flex-col items-center justify-center bg-purple-50 rounded-2xl border border-purple-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-purple-500 mb-2">&#128101;</span>
              <span className="font-medium text-lg">View Applications</span>
            </div>
            <div className="flex flex-col items-center justify-center bg-green-50 rounded-2xl border border-green-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-green-500 mb-2">&#8599;</span>
              <span className="font-medium text-lg">Withdraw Earnings</span>
            </div>
            <div className="flex flex-col items-center justify-center bg-yellow-50 rounded-2xl border border-yellow-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-yellow-500 mb-2">&#128222;</span>
              <span className="font-medium text-lg">Contact Support</span>
            </div>
          </div>
        </div>
      </div>
      {/* Welcome Card */}
      <div className="bg-white rounded-xl shadow p-6 mb-2">
        <h2 className="text-2xl font-bold mb-1">Welcome back, Jane!</h2>
        <p className="text-gray-600 text-sm">Your lending portfolio is performing well with 8 active loans and ₦1.3M in total earnings.</p>
        <div className="mt-4">
          <Link href="/dashboard/lender/my-offers">
            <Button variant="outline" size="sm" className="text-blue-600 hover:text-blue-700">
              View All My Offers
            </Button>
          </Link>
        </div>
      </div>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-2">
        {stats.map((stat) => (
          <div key={stat.label} className="bg-white rounded-xl shadow p-4 flex flex-col items-start relative">
            <div className="text-2xl mb-2">{stat.icon}</div>
            <div className="text-lg font-semibold">{stat.value}</div>
            <div className="text-xs text-gray-500">{stat.label}</div>
            {stat.link && (
              <a href={stat.link} className="absolute top-3 right-4 text-xs text-blue-600 hover:underline">{stat.sub}</a>
            )}
          </div>
        ))}
      </div>
      {/* Main Content: Applications & Earnings */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Latest Borrower Applications */}
        <div className="md:col-span-2 bg-white rounded-xl shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Latest Borrower Applications</h3>
            <a href="#" className="text-blue-600 text-sm hover:underline">View All</a>
          </div>
          <div className="space-y-4">
            {applications.map((app) => (
              <div key={app.name} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                <div className="flex items-center gap-3">
                  <Image 
                    src={app.avatar} 
                    alt={app.name} 
                    width={40} 
                    height={40} 
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div>
                    <div className="font-medium">{app.name}</div>
                    <div className="text-xs text-gray-500">{app.amount} • {app.type}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-xs px-2 py-1 rounded ${app.status === "New" ? "bg-blue-100 text-blue-700" : "bg-yellow-100 text-yellow-700"}`}>{app.status}</span>
                  <button className="bg-indigo-600 text-white px-4 py-1.5 rounded hover:bg-indigo-700 text-sm font-medium">Review</button>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Earnings Snapshot */}
        <div className="bg-white rounded-xl shadow p-6 flex flex-col justify-between">
          <div>
            <h3 className="text-lg font-semibold mb-4">Earnings Snapshot</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between"><span>This Month</span><span className="font-semibold">{earnings.month}</span></div>
              <div className="flex justify-between"><span>Disbursed</span><span className="text-green-600 font-semibold">{earnings.disbursed}</span></div>
              <div className="flex justify-between"><span>Repaid</span><span className="text-blue-600 font-semibold">{earnings.repaid}</span></div>
            </div>
          </div>
          <button className="mt-6 w-full bg-gray-100 text-gray-700 py-2 rounded hover:bg-gray-200 font-medium">View Full Report</button>
        </div>
      </div>
    </div>
  );
} 
