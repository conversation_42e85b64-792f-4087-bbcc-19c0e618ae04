"use client";

import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { lenders } from "@/data/mockLenders";

export default function MarketplacePage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"individual" | "corporate">("individual");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAmount, setSelectedAmount] = useState("");

  const filteredLenders = lenders[activeTab].filter(lender => {
    const matchesSearch = lender.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lender.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesAmount = !selectedAmount || 
      (parseInt(selectedAmount) >= parseInt(lender.minAmount.replace(/[^0-9]/g, '')) &&
       parseInt(selectedAmount) <= parseInt(lender.maxAmount.replace(/[^0-9]/g, '')));
    return matchesSearch && matchesAmount;
  });

  const amountOptions = [
    "₦50,000",
    "₦100,000",
    "₦200,000",
    "₦500,000",
    "₦1,000,000",
    "₦2,000,000",
    "₦5,000,000",
    "₦10,000,000",
  ];

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Lender Marketplace</h1>
        <p className="text-gray-500 text-sm mt-1">
          Find and connect with lenders that match your needs
        </p>
      </div>

      {/* Tabs */}
      <div className="flex gap-4 mb-6">
        <Button
          variant={activeTab === "individual" ? "default" : "outline"}
          onClick={() => setActiveTab("individual")}
          className="flex-1"
        >
          Individual Lenders
        </Button>
        <Button
          variant={activeTab === "corporate" ? "default" : "outline"}
          onClick={() => setActiveTab("corporate")}
          className="flex-1"
        >
          Corporate Lenders
        </Button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Input
          placeholder="Search by lender name or specialty..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="md:col-span-2"
        />
        <select
          value={selectedAmount}
          onChange={(e) => setSelectedAmount(e.target.value)}
          className="rounded-md border border-gray-300 px-3 py-2 text-sm"
        >
          <option value="">All Amounts</option>
          {amountOptions.map(amount => (
            <option key={amount} value={amount}>{amount}</option>
          ))}
        </select>
      </div>

      {/* Lenders Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredLenders.map((lender) => (
          <Card key={lender.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{lender.icon}</span>
                  <div>
                    <CardTitle className="text-lg">{lender.name}</CardTitle>
                    <p className="text-sm text-gray-500">{lender.type}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1">
                    <span className="text-yellow-500">★</span>
                    <span className="font-medium">{lender.rating}</span>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Loan Range</p>
                    <p className="font-semibold">{lender.minAmount} - {lender.maxAmount}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Interest Rate</p>
                    <p className="font-semibold">{lender.interestRate}</p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => router.push(`/dashboard/borrower/marketplace/${lender.id}`)}
                  >
                    View Details
                  </Button>
                  <Button
                    className="flex-1"
                    onClick={() => router.push(`/dashboard/borrower/marketplace/${lender.id}`)}
                  >
                    Apply Now
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}