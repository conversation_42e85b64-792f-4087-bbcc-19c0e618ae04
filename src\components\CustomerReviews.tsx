"use client";
import React from 'react';
import { Card, CardContent } from './ui/card';
import Image from 'next/image';

const StarIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M12 .587l3.668 7.568 8.332 1.151-6.064 5.828 1.48 8.279-7.416-3.967-7.417 3.967 1.481-8.279-6.064-5.828 8.332-1.151z"/>
  </svg>
);

const CustomerReviews = () => {
  return (
    <div className="bg-gray-50 py-12 sm:py-16">
      <div className="max-w-4xl mx-auto text-center px-4">
        <div className="inline-flex flex-col sm:flex-row items-center">
          <h2 className="text-3xl sm:text-4xl font-bold mr-0 sm:mr-4">Excellent</h2>
          <div className="flex mt-2 sm:mt-0">
            <StarIcon className="text-yellow-400 w-6 h-6" />
            <StarIcon className="text-yellow-400 w-6 h-6" />
            <StarIcon className="text-yellow-400 w-6 h-6" />
            <StarIcon className="text-yellow-400 w-6 h-6" />
            <StarIcon className="text-yellow-400 w-6 h-6" />
          </div>
        </div>
        <p className="text-gray-500 mt-2 text-sm sm:text-base">Read 4.8 out of 5 based on 613 reviews</p>
      </div>
      <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto px-4">
        <Card className="bg-white p-6 rounded-lg shadow-md">
          <CardContent className="text-center">
            <Image src="/Ellipse 2.svg" alt="James Coker" width={80} height={80} className="rounded-full mx-auto" />
            <h3 className="font-bold mt-4">Fast and Reliable</h3>
            <p className="text-gray-600 mt-2">"The process was transparent and secure. I was able to close my business loan in record time"</p>
            <div className="flex items-center justify-center mt-4">
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
            </div>
            <p className="font-bold mt-4">James Coker</p>
            <p className="text-sm text-gray-500">Nigeria, 30th March, 2025</p>
            <p className="text-sm text-gray-500">Borrower</p>
          </CardContent>
        </Card>
        <Card className="bg-white p-6 rounded-lg shadow-md">
          <CardContent className="text-center">
            <Image src="/Ellipse 2 (1).svg" alt="Shawn Murphy" width={80} height={80} className="rounded-full mx-auto" />
            <h3 className="font-bold mt-4">Great Analytics</h3>
            <p className="text-gray-600 mt-2">"The Platform's KYC and analytics tools help us lend confidently to the right people. Highly recommended"</p>
            <div className="flex items-center justify-center mt-4">
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-gray-300 w-5 h-5" />
            </div>
            <p className="font-bold mt-4">Shawn Murphy</p>
            <p className="text-sm text-gray-500">Sierra Leone, 4th May, 2025</p>
            <p className="text-sm text-gray-500">Lender</p>
          </CardContent>
        </Card>
        <Card className="bg-white p-6 rounded-lg shadow-md">
          <CardContent className="text-center">
            <Image src="/Ellipse 2 (2).svg" alt="Adams Grey" width={80} height={80} className="rounded-full mx-auto" />
            <h3 className="font-bold mt-4">Financing Made Simple</h3>
            <p className="text-gray-600 mt-2">"Kredxa made comparing and applying for a loan so easy. I found the right offer and got the funds quickly"</p>
            <div className="flex items-center justify-center mt-4">
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
              <StarIcon className="text-yellow-400 w-5 h-5" />
            </div>
            <p className="font-bold mt-4">Adams Grey</p>
            <p className="text-sm text-gray-500">Garbon, 15th July, 2025</p>
            <p className="text-sm text-gray-500">Borrower</p>
          </CardContent>
        </Card>
      </div>
      <div className="flex justify-center mt-8 space-x-4">
        <button className="p-2 rounded-full border">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button className="p-2 rounded-full border">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default CustomerReviews;