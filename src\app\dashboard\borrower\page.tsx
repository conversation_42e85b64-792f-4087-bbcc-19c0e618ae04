import React from "react";
import Image from "next/image";
import Link from "next/link";
import { loanOffers } from "@/data/mockLenders";

const stats = [
  { label: "Active Loans", value: 2, sub: "₦620,000 total", icon: "💳" },
  { label: "Next Payment", value: "12 Days", sub: "₦83,333 due", icon: "📅" },
  { label: "Total Repaid", value: "₦186,666", sub: "30% complete", icon: "✅" },
  { label: "Available Offers", value: 12, sub: "View Marketplace", icon: "🎁", link: "/dashboard/borrower/marketplace" },
];

const payments = [
  {
    id: "LN-001234",
    due: "June 12, 2025",
    amount: "₦83,333",
    warning: true,
  },
  {
    id: "LN-001235",
    due: "May 28, 2025",
    amount: "₦20,000",
    warning: false,
  },
];

export default function BorrowerDashboard() {
  // Get top recommendations from both individual and corporate lenders
  const topIndividualOffers = loanOffers.individual
    .filter(offer => offer.status === 'active')
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 2);

  const topCorporateOffers = loanOffers.corporate
    .filter(offer => offer.status === 'active')
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 2);

  const allRecommendations = [...topIndividualOffers, ...topCorporateOffers]
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 6);

  return (
    <div className="space-y-6">
      {/* Welcome Card */}
      <div className="flex items-center justify-between bg-white rounded-xl shadow p-6">
        <div>
          <h2 className="text-2xl font-bold mb-1">Welcome back, John!</h2>
          <p className="text-gray-600 text-sm">Your loan journey continues. 2 active loans, next payment due in 12 days.</p>
        </div>
        <Image 
          src="https://randomuser.me/api/portraits/men/32.jpg" 
          alt="User avatar" 
          width={64} 
          height={64} 
          className="w-16 h-16 rounded-full border-2 border-gray-200"
        />
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <div key={stat.label} className="bg-white rounded-xl shadow p-4 flex flex-col items-start">
            <div className="text-2xl mb-2">{stat.icon}</div>
            <div className="text-lg font-semibold">{stat.value}</div>
            <div className="text-xs text-gray-500">{stat.sub}</div>
            {stat.link && (
              <Link href={stat.link} className="text-xs text-black mt-2 hover:underline">
                {stat.sub}
              </Link>
            )}
          </div>
        ))}
      </div>
      
      {/* Upcoming Payments */}
      <div className="bg-white rounded-xl shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Upcoming Payments</h3>
        <div className="space-y-3">
          {payments.map((p) => (
            <div
              key={p.id}
              className={`flex items-center justify-between rounded-lg p-4 ${p.warning ? "bg-yellow-100" : "bg-blue-50"}`}
            >
              <div className="flex items-center gap-3">
                <span className="text-xl">
                  {p.warning ? "\u26A0\uFE0F" : "\u25CF"}
                </span>
                <div>
                  <div className="font-medium">Loan #{p.id}</div>
                  <div className="text-xs text-gray-600">Due: {p.due}</div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="font-semibold text-lg">{p.amount}</div>
                <button className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800">Pay Now</button>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Additional Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mt-4">
        <Link href="/dashboard/borrower/marketplace" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-indigo-600 mb-2">+</span>
          <span className="font-medium text-sm">Apply for New Loan</span>
        </Link>
        <Link href="/dashboard/settings" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-green-600 mb-2">&#128179;</span>
          <span className="font-medium text-sm">Update KYC</span>
        </Link>
        <Link href="/dashboard/borrower/my-loans" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-blue-600 mb-2">&#8635;</span>
          <span className="font-medium text-sm">View Loan History</span>
        </Link>
        <Link href="/dashboard/support" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-orange-600 mb-2">&#128222;</span>
          <span className="font-medium text-sm">Contact Support</span>
        </Link>
      </div>
      
      {/* Recommended Offers */}
      <div className="bg-white rounded-xl shadow p-6 mt-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h3 className="text-lg font-semibold">Recommended for You</h3>
            <p className="text-sm text-gray-600 mt-1">Top picks from individual and corporate lenders</p>
          </div>
          <Link href="/dashboard/borrower/marketplace" className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800 text-sm font-medium">
            Go to Marketplace
          </Link>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {allRecommendations.map((offer) => (
            <div key={offer.id} className="bg-white rounded-3xl p-6 shadow-md border border-gray-100 flex flex-col h-fit">
              {/* Lender Header */}
              <div className="flex items-center justify-between mb-6 flex-wrap gap-3">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center overflow-hidden">
                    <span className="text-2xl">{offer.lenderIcon}</span>
                  </div>
                  <div className="min-w-0">
                    <span className="text-xl font-bold text-[#1a1a1a] break-words block">
                      {offer.lenderName}
                    </span>
                    <span className="text-sm text-gray-600 block">
                      {offer.productName}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-1.5 flex-shrink-0">
                  <span className="text-yellow-500">★</span>
                  <span className="text-sm font-medium text-[#1a1a1a]">{offer.rating}</span>
                </div>
              </div>

              {/* Loan Details */}
              <div className="flex flex-col gap-4 mb-6">
                {[
                  { label: "Loan Amount", value: `₦${offer.minAmount} - ₦${offer.maxAmount}` },
                  { label: "Interest Rate", value: `${offer.interestRate} ${offer.rateType}` },
                  { label: "Duration", value: `${offer.minDuration}-${offer.maxDuration} months` },
                  { label: "Collateral", value: offer.collateralRequired ? "Required" : "Not Required" },
                  { label: "Processing Fee", value: `${offer.processingFee}%` },
                ].map((item, index) => (
                  <div key={index} className="flex justify-between items-start gap-4">
                    <span className="text-sm text-gray-600 font-medium">{item.label}</span>
                    <span className="text-sm text-[#1a1a1a] font-semibold text-right">{item.value}</span>
                  </div>
                ))}
              </div>

              {/* Target Borrowers */}
              <div className="mb-6">
                <span className="text-sm text-gray-600 font-medium block mb-2">Target Borrowers</span>
                <div className="flex flex-wrap gap-1">
                  {offer.targetBorrowers.slice(0, 2).map((borrower: string, index: number) => (
                    <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {borrower}
                    </span>
                  ))}
                  {offer.targetBorrowers.length > 2 && (
                    <span className="text-xs text-gray-500">+{offer.targetBorrowers.length - 2} more</span>
                  )}
                </div>
              </div>

              {/* Action Button */}
              <div className="mt-auto">
                <Link 
                  href={`/dashboard/borrower/marketplace/${offer.id}`} 
                  className="w-full bg-[#2D0A0A] text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-[#1a0808] transition-colors text-center block"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 
