"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Eye, 
  Edit, 
  Pause, 
  Play, 
  Trash2, 
  Users, 
  TrendingUp,
  Plus,
  ArrowLeft,
  X,
  Save
} from "lucide-react";
import Link from 'next/link';
import { loanOffers, LoanOffer } from "@/data/mockLenders";

const targetBorrowerOptions = [
  "Individual borrowers",
  "Small business owners",
  "Startups",
  "Established businesses",
  "Freelancers",
  "Students"
];

export default function MyOffers() {
  const [offers, setOffers] = useState<LoanOffer[]>([
    ...loanOffers.individual,
    ...loanOffers.corporate
  ]);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<LoanOffer | null>(null);
  const [editFormData, setEditFormData] = useState<LoanOffer | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Play className="w-4 h-4" />;
      case 'paused':
        return <Pause className="w-4 h-4" />;
      case 'draft':
        return <Edit className="w-4 h-4" />;
      default:
        return <Edit className="w-4 h-4" />;
    }
  };

  const toggleOfferStatus = (offerId: string) => {
    setOffers(prev => prev.map(offer => {
      if (offer.id === offerId) {
        const newStatus = offer.status === 'active' ? 'paused' : 'active';
        return { ...offer, status: newStatus };
      }
      return offer;
    }));
  };

  const deleteOffer = (offerId: string) => {
    if (confirm('Are you sure you want to delete this loan offer? This action cannot be undone.')) {
      setOffers(prev => prev.filter(offer => offer.id !== offerId));
    }
  };

  const openViewModal = (offer: LoanOffer) => {
    setSelectedOffer(offer);
    setViewModalOpen(true);
  };

  const openEditModal = (offer: LoanOffer) => {
    setSelectedOffer(offer);
    setEditFormData({ ...offer });
    setEditModalOpen(true);
  };

  const closeModals = () => {
    setViewModalOpen(false);
    setEditModalOpen(false);
    setSelectedOffer(null);
    setEditFormData(null);
  };

  const handleEditSubmit = () => {
    if (editFormData) {
      setOffers(prev => prev.map(offer => 
        offer.id === editFormData.id ? editFormData : offer
      ));
      closeModals();
    }
  };

  const toggleTargetBorrower = (borrower: string) => {
    if (editFormData) {
      setEditFormData(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          targetBorrowers: prev.targetBorrowers.includes(borrower)
            ? prev.targetBorrowers.filter(b => b !== borrower)
            : [...prev.targetBorrowers, borrower]
        };
      });
    }
  };

  const stats = {
    totalOffers: offers.length,
    activeOffers: offers.filter(offer => offer.status === 'active').length,
    totalApplications: offers.reduce((sum, offer) => sum + offer.totalApplications, 0),
    totalDisbursed: offers.reduce((sum, offer) => {
      const amount = parseFloat(offer.totalDisbursed.replace(/[^0-9.]/g, ''));
      return sum + (isNaN(amount) ? 0 : amount);
    }, 0)
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/lender">
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Loan Offers</h1>
            <p className="text-sm text-gray-600">Manage your loan offers and track their performance</p>
          </div>
        </div>
        <Link href="/dashboard/lender/create-offer">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <Plus className="w-4 h-4 mr-2" />
            Create New Offer
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Offers</p>
                <p className="text-2xl font-bold">{stats.totalOffers}</p>
              </div>
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Offers</p>
                <p className="text-2xl font-bold">{stats.activeOffers}</p>
              </div>
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Play className="w-5 h-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Applications</p>
                <p className="text-2xl font-bold">{stats.totalApplications}</p>
              </div>
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="w-5 h-5 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Disbursed</p>
                <p className="text-2xl font-bold">₦{(stats.totalDisbursed / 1000000).toFixed(1)}M</p>
              </div>
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Offers List */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-900">Loan Offers</h2>
        </CardHeader>
        <CardContent>
          {offers.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No loan offers yet</h3>
              <p className="text-gray-600 mb-4">Create your first loan offer to start lending to borrowers</p>
              <Link href="/dashboard/lender/create-offer">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Offer
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {offers.map((offer) => (
                <div key={offer.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-gray-900">{offer.productName}</h3>
                        <Badge className={getStatusColor(offer.status)}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(offer.status)}
                            {offer.status.charAt(0).toUpperCase() + offer.status.slice(1)}
                          </span>
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{offer.description}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Amount Range:</span>
                          <p className="font-medium">₦{offer.minAmount} - ₦{offer.maxAmount}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Interest Rate:</span>
                          <p className="font-medium">{offer.interestRate} {offer.rateType}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Applications:</span>
                          <p className="font-medium">{offer.totalApplications}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Total Disbursed:</span>
                          <p className="font-medium">{offer.totalDisbursed}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => openViewModal(offer)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => openEditModal(offer)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => toggleOfferStatus(offer.id)}
                      >
                        {offer.status === 'active' ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => deleteOffer(offer.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Modal */}
      <Dialog open={viewModalOpen} onOpenChange={setViewModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Loan Offer Details</span>
              <Button variant="ghost" size="sm" onClick={closeModals}>
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {selectedOffer && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-gray-600">Product Name</Label>
                    <p className="font-medium">{selectedOffer.productName}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Status</Label>
                    <Badge className={getStatusColor(selectedOffer.status)}>
                      {selectedOffer.status.charAt(0).toUpperCase() + selectedOffer.status.slice(1)}
                    </Badge>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Amount Range</Label>
                    <p className="font-medium">₦{selectedOffer.minAmount} - ₦{selectedOffer.maxAmount}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Duration Range</Label>
                    <p className="font-medium">{selectedOffer.minDuration} - {selectedOffer.maxDuration} months</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Interest Rate</Label>
                    <p className="font-medium">{selectedOffer.interestRate} {selectedOffer.rateType}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Processing Fee</Label>
                    <p className="font-medium">{selectedOffer.processingFee}%</p>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <Label className="text-sm text-gray-600">Description</Label>
                <p className="font-medium mt-1">{selectedOffer.description}</p>
              </div>

              {/* Target Borrowers */}
              <div>
                <Label className="text-sm text-gray-600">Target Borrowers</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedOffer.targetBorrowers.map((borrower) => (
                    <Badge key={borrower} variant="secondary">
                      {borrower}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Requirements */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-gray-600">Collateral Required</Label>
                  <p className="font-medium">{selectedOffer.collateralRequired ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Created Date</Label>
                  <p className="font-medium">{selectedOffer.createdAt}</p>
                </div>
              </div>

              {/* Performance */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Performance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-gray-600">Total Applications</Label>
                    <p className="font-medium">{selectedOffer.totalApplications}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Total Disbursed</Label>
                    <p className="font-medium">{selectedOffer.totalDisbursed}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Edit Loan Offer</span>
              <Button variant="ghost" size="sm" onClick={closeModals}>
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {editFormData && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="editProductName" className="text-sm font-medium text-gray-700">
                    Product Name *
                  </Label>
                  <Input
                    id="editProductName"
                    value={editFormData.productName}
                    onChange={(e) => setEditFormData({ ...editFormData, productName: e.target.value })}
                    className="w-full"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="editMinAmount" className="text-sm text-gray-600">
                      Minimum Amount (₦)
                    </Label>
                    <Input
                      id="editMinAmount"
                      value={editFormData.minAmount}
                      onChange={(e) => setEditFormData({ ...editFormData, minAmount: e.target.value })}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="editMaxAmount" className="text-sm text-gray-600">
                      Maximum Amount (₦)
                    </Label>
                    <Input
                      id="editMaxAmount"
                      value={editFormData.maxAmount}
                      onChange={(e) => setEditFormData({ ...editFormData, maxAmount: e.target.value })}
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Minimum Duration (months)</Label>
                    <Select
                      value={editFormData.minDuration}
                      onValueChange={(value) => setEditFormData({ ...editFormData, minDuration: value })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[1, 2, 3, 4, 5, 6].map((month) => (
                          <SelectItem key={month} value={month.toString()}>{month}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Maximum Duration (months)</Label>
                    <Select
                      value={editFormData.maxDuration}
                      onValueChange={(value) => setEditFormData({ ...editFormData, maxDuration: value })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[6, 12, 18, 24, 36].map((month) => (
                          <SelectItem key={month} value={month.toString()}>{month}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="flex-1">
                    <Label htmlFor="editInterestRate" className="text-sm font-medium text-gray-700">Interest Rate *</Label>
                    <Input
                      id="editInterestRate"
                      value={editFormData.interestRate}
                      onChange={(e) => setEditFormData({ ...editFormData, interestRate: e.target.value })}
                      className="w-full"
                    />
                  </div>
                  <div className="w-full sm:w-40">
                    <Label className="text-sm font-medium text-gray-700">Rate Type</Label>
                    <Select
                      value={editFormData.rateType}
                      onValueChange={(value) => setEditFormData({ ...editFormData, rateType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="% Monthly">% Monthly</SelectItem>
                        <SelectItem value="% Annually">% Annually</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="editProcessingFee" className="text-sm font-medium text-gray-700">
                    Processing Fee (%)
                  </Label>
                  <Input
                    id="editProcessingFee"
                    value={editFormData.processingFee}
                    onChange={(e) => setEditFormData({ ...editFormData, processingFee: e.target.value })}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Target Borrowers */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Target Borrowers</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {targetBorrowerOptions.map((borrower) => (
                    <div key={borrower} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`edit-${borrower}`}
                        checked={editFormData.targetBorrowers.includes(borrower)}
                        onChange={() => toggleTargetBorrower(borrower)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Label htmlFor={`edit-${borrower}`} className="text-sm text-gray-700">{borrower}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Collateral Requirement */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Collateral Requirement</Label>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="edit-collateral-required"
                      name="edit-collateral"
                      checked={editFormData.collateralRequired}
                      onChange={() => setEditFormData({ ...editFormData, collateralRequired: true })}
                      className="text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="edit-collateral-required" className="text-sm text-gray-700">Collateral Required</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="edit-collateral-not-required"
                      name="edit-collateral"
                      checked={!editFormData.collateralRequired}
                      onChange={() => setEditFormData({ ...editFormData, collateralRequired: false })}
                      className="text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="edit-collateral-not-required" className="text-sm text-gray-700">No Collateral Required</Label>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="editDescription" className="text-sm font-medium text-gray-700">
                  Loan Description
                </Label>
                <Textarea
                  id="editDescription"
                  value={editFormData.description}
                  onChange={(e) => setEditFormData({ ...editFormData, description: e.target.value })}
                  className="w-full min-h-[100px] resize-none"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4">
                <Button variant="outline" onClick={closeModals}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleEditSubmit}
                  disabled={!editFormData.productName}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 