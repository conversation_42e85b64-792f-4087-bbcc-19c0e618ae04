"use client";
import Link from "next/link";
import Image from "next/image";
import { useState } from "react";

export default function Header() {
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <header className="relative mb-3">
      <div className="flex flex-col py-4 px-4 md:px-16 border-t border-gray-100 mt-0">
        {/* Social Media Icons */}
        <div className="flex justify-end gap-4 mb-2">
          <Link href="#" aria-label="Facebook">
            <Image src="/facebook.svg" alt="Facebook" width={20} height={20} />
          </Link>
          <Link href="#" aria-label="Twitter">
            <Image src="/twitter.svg" alt="Twitter" width={20} height={20} />
          </Link>
          <Link href="#" aria-label="LinkedIn">
            <Image src="/linkedin.svg" alt="LinkedIn" width={20} height={20} />
          </Link>
          <Link href="#" aria-label="Instagram">
            <Image src="/instagram.svg" alt="Instagram" width={20} height={20} />
          </Link>
        </div>
      </div>
      <div className="w-full border-b border-gray-200 mb-4"></div> 
      <div className="flex justify-between items-center px-4 md:px-16 relative">
        <div className="flex items-center gap-2">
          <h1 className="font-bold text-xl">Kredxa</h1>
        </div>
        {/* Hamburger for mobile - always top right */}
        <button
          className="md:hidden flex flex-col justify-center items-center w-10 h-10 absolute right-4 top-1/2 -translate-y-1/2"
          onClick={() => setMenuOpen(!menuOpen)}
          aria-label="Open menu"
        >
          <span className="block w-6 h-0.5 bg-black mb-1"></span>
          <span className="block w-6 h-0.5 bg-black mb-1"></span>
          <span className="block w-6 h-0.5 bg-black"></span>
        </button>
        {/* Desktop nav */}
        <nav className="hidden md:flex items-center gap-8">
          <Link href="/" className="font-medium">HOME</Link>
          <Link href="/about" className="font-medium">ABOUT</Link>
          <Link href="/lend" className="font-medium">LEND</Link>
          <Link href="/borrow" className="font-medium">BORROW</Link>
          <Link href="/marketplace" className="font-medium">MARKETPLACE</Link>
          <Link href="/pages" className="font-medium">PAGES</Link>
          <Link
            href="/get-started"
            className="bg-[#1A0505] text-white px-4 py-2 rounded-full text-center"
          >
            GET STARTED
          </Link>
        </nav>
      </div>
      {/* Mobile menu */}
      {menuOpen && (
        <div className="md:hidden px-4 pb-4">
          <nav className="flex flex-col gap-4 mt-2">
            <Link href="/" className="font-medium" onClick={() => setMenuOpen(false)}>HOME</Link>
            <Link href="/about" className="font-medium" onClick={() => setMenuOpen(false)}>ABOUT</Link>
            <Link href="/lend" className="font-medium" onClick={() => setMenuOpen(false)}>LEND</Link>
            <Link href="/borrow" className="font-medium" onClick={() => setMenuOpen(false)}>BORROW</Link>
            <Link href="/marketplace" className="font-medium" onClick={() => setMenuOpen(false)}>MARKETPLACE</Link>
            <Link href="/pages" className="font-medium" onClick={() => setMenuOpen(false)}>PAGES</Link>
            <Link href="/get-started" className="bg-[#1A0505] text-white px-4 py-2 rounded-full text-center" onClick={() => setMenuOpen(false)}>
              GET STARTED
            </Link>
          </nav>
        </div>
      )}
    </header>
  );
}







