"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut } from "@/lib/auth-supabase";
import { useRouter } from "next/navigation";
import {
  LayoutDashboard,
  Users,
  Building,
  LogOut,
  Settings,
  ShieldCheck,
} from "lucide-react";

export default function AdminSidebar() {
  const pathname = usePathname();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut();
    router.push("/auth/corporate/login");
  };

  const navLinks = [
    { href: "/admin", label: "Dashboard", icon: LayoutDashboard },
    { href: "/admin/individuals", label: "Individuals", icon: Users },
    { href: "/admin/corporates", label: "Corporates", icon: Building },
  ];

  const bottomLinks = [
    { href: "/admin/settings", label: "Settings", icon: Settings },
    { href: "/admin/setup", label: "Admin Setup", icon: ShieldCheck },
  ];

  const activeLinkClasses = "bg-gray-700 text-white";
  const linkClasses =
    "flex items-center px-4 py-2.5 text-sm font-medium rounded-lg hover:bg-gray-700 hover:text-white transition-colors";

  return (
    <aside className="w-64 bg-gray-800 text-gray-300 flex flex-col">
      <div className="p-4 border-b border-gray-700">
        <Link href="/admin">
          <span className="text-xl font-bold text-white">Kredxa Admin</span>
        </Link>
      </div>
      <nav className="flex-1 px-4 py-4 space-y-2">
        {navLinks.map((link) => (
          <Link
            key={link.href}
            href={link.href}
            className={`${linkClasses} ${
              pathname === link.href ? activeLinkClasses : ""
            }`}
          >
            <link.icon className="mr-3 h-5 w-5" />
            {link.label}
          </Link>
        ))}
      </nav>
      <div className="px-4 py-4 border-t border-gray-700 space-y-2">
        {bottomLinks.map((link) => (
            <Link
                key={link.href}
                href={link.href}
                className={`${linkClasses} ${
                pathname === link.href ? activeLinkClasses : ""
                }`}
            >
                <link.icon className="mr-3 h-5 w-5" />
                {link.label}
            </Link>
        ))}
        <button onClick={handleSignOut} className={`${linkClasses} w-full`}>
          <LogOut className="mr-3 h-5 w-5" />
          Logout
        </button>
      </div>
    </aside>
  );
} 