import { supabase } from './supabase';
import type { User } from '@supabase/supabase-js';

export interface AuthResult {
  success: boolean;
  error?: string;
  user?: User | null;
}

export interface IndividualSignupData {
  email: string;
  password: string;
  bvn?: string;
  dateOfBirth?: string;
  name?: string;
  phoneNumber?: string;
}

export interface CorporateSignupData {
  email: string;
  password: string;
  organizationName?: string;
  officeAddress?: string;
  contactPerson?: string;
  contactPhone?: string;
  businessType?: string;
  registrationNumber?: string;
  taxIdentificationNumber?: string;
  website?: string;
  industry?: string;
  companySize?: '1-10' | '11-50' | '51-200' | '201-500' | '500+';
  yearsInBusiness?: number;
  annualRevenue?: number;
  bankName?: string;
  accountNumber?: string;
  accountName?: string;
  documents?: {
    cac?: File | null;
    mermet?: File | null;
    utilityBill?: File | null;
    lendersLicense?: File | null;
  };
}

// Test function to check database connectivity and table existence
export async function testDatabaseConnection() {
  try {
    console.log('Testing database connection...');
    
    // First, test basic Supabase connection
    const { error: authError } = await supabase.auth.getSession();
    if (authError) {
      console.error('Supabase auth error:', authError);
      return { success: false, error: `Supabase connection failed: ${authError.message}` };
    }
    
    console.log('Supabase connection successful');
    
    // Test if individual_accounts table exists by trying to select from it
    const { error: individualError } = await supabase
      .from('individual_accounts')
      .select('id')
      .limit(1);
    
    if (individualError) {
      console.error('Individual accounts table error:', individualError);
      return { success: false, error: `Individual accounts table error: ${individualError.message}` };
    }
    
    console.log('Individual accounts table accessible');
    
    // Test if corporate_accounts table exists
    const { error: corporateError } = await supabase
      .from('corporate_accounts')
      .select('id')
      .limit(1);
    
    if (corporateError) {
      console.error('Corporate accounts table error:', corporateError);
      return { success: false, error: `Corporate accounts table error: ${corporateError.message}` };
    }
    
    console.log('Corporate accounts table accessible');
    
    // Test if profiles table exists
    const { error: profilesError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (profilesError) {
      console.error('Profiles table error:', profilesError);
      return { success: false, error: `Profiles table error: ${profilesError.message}` };
    }
    
    console.log('Profiles table accessible');
    
    return { success: true, message: 'All tables accessible' };
  } catch (error) {
    console.error('Database connection test error:', error);
    return { success: false, error: 'Database connection failed' };
  }
}

// Individual signup with BVN verification
export async function signUpIndividual(
  email: string, 
  password: string, 
  additionalData?: Partial<IndividualSignupData>
): Promise<AuthResult> {
  try {
    console.log('Starting individual signup for:', email);
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          user_type: 'individual',
          mode: 'borrower' // Default mode for individuals
        }
      }
    });

    if (error) {
      console.error('Auth signup error:', error);
      return { success: false, error: error.message };
    }

    console.log('Auth signup successful, user ID:', data.user?.id);

    if (data.user) {
      // Create individual account record
      const insertData = {
        user_id: data.user.id,
        email: email,
        bvn: additionalData?.bvn || null,
        date_of_birth: additionalData?.dateOfBirth || null,
        full_name: additionalData?.name || null,
        phone_number: additionalData?.phoneNumber || null,
        verification_status: 'pending'
      };
      
      console.log('Inserting individual account data:', insertData);
      
      const { error: profileError } = await supabase
        .from('individual_accounts')
        .insert(insertData);

      if (profileError) {
        console.error('Error creating individual account:', profileError);
        // Don't fail the signup if profile creation fails
      } else {
        console.log('Individual account created successfully');
      }
    }

    return { success: true, user: data.user };
  } catch (error) {
    console.error('Signup error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Corporate signup with organization details
export async function signUpCorporate(
  email: string, 
  password: string, 
  additionalData?: Partial<CorporateSignupData>
): Promise<AuthResult> {
  try {
    console.log('Starting corporate signup for:', email);
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          user_type: 'corporate',
          mode: 'lender' // Default mode for corporate
        }
      }
    });

    if (error) {
      console.error('Auth signup error:', error);
      return { success: false, error: error.message };
    }

    console.log('Auth signup successful, user ID:', data.user?.id);

    if (data.user) {
      // Create corporate account record
      const insertData = {
        user_id: data.user.id,
        email: email,
        organization_name: additionalData?.organizationName || null,
        office_address: additionalData?.officeAddress || null,
        approval_status: 'pending',
        verification_status: 'pending'
      };
      
      console.log('Inserting corporate account data:', insertData);
      
      const { error: profileError } = await supabase
        .from('corporate_accounts')
        .insert(insertData);

      if (profileError) {
        console.error('Error creating corporate account:', profileError);
        // Don't fail the signup if profile creation fails, but log it
      } else {
        console.log('Corporate account created successfully');
      }

      // Handle document uploads if provided
      if (additionalData?.documents) {
        await handleDocumentUploads(data.user.id, additionalData.documents);
      }
    }

    return { success: true, user: data.user };
  } catch (error) {
    console.error('Corporate signup error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Handle document uploads for corporate accounts
async function handleDocumentUploads(userId: string, documents: CorporateSignupData['documents']) {
  if (!documents) return;

  const documentTypes = ['cac', 'mermet', 'utilityBill', 'lendersLicense'] as const;
  
  for (const docType of documentTypes) {
    const file = documents[docType];
    if (file) {
      try {
        console.log(`Uploading ${docType} document for user ${userId}`);
        
        // Upload file to Supabase Storage
        const fileName = `${userId}/${docType}_${Date.now()}_${file.name}`;
        const { error: uploadError } = await supabase.storage
          .from('documents')
          .upload(fileName, file);

        if (uploadError) {
          console.error(`Error uploading ${docType}:`, uploadError);
          continue;
        }

        console.log(`Successfully uploaded ${docType} to storage`);

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('documents')
          .getPublicUrl(fileName);

        // Save document record
        const { error: dbError } = await supabase
          .from('documents')
          .insert({
            user_id: userId,
            document_type: docType,
            file_name: fileName,
            file_url: urlData.publicUrl,
            status: 'pending'
          });

        if (dbError) {
          console.error(`Error saving ${docType} record:`, dbError);
        } else {
          console.log(`Successfully saved ${docType} record to database`);
        }

      } catch (error) {
        console.error(`Error processing ${docType}:`, error);
      }
    }
  }
}

// Login function
export async function signIn(email: string, password: string): Promise<AuthResult> {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, user: data.user };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Logout function
export async function signOut(): Promise<AuthResult> {
  try {
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Password reset
export async function resetPassword(email: string): Promise<AuthResult> {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Password reset error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Update password
export async function updatePassword(newPassword: string): Promise<AuthResult> {
  try {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Password update error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Get current user
export async function getCurrentUser() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('Error getting user:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// Get corporate account details
export async function getCorporateAccount(userId: string) {
  try {
    console.log('Getting corporate account for user:', userId);
    
    const { data, error } = await supabase
      .from('corporate_accounts')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error getting corporate account:', error);
      return null;
    }

    console.log('Corporate account retrieved successfully:', data);
    return data;
  } catch (error) {
    console.error('Error getting corporate account:', error);
    return null;
  }
}

// Get user profile
export async function getUserProfile(userId: string) {
  try {
    console.log('Getting user profile for:', userId);
    
    // Check individual account first
    const { data: individualData, error: individualError } = await supabase
      .from('individual_accounts')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (individualData && !individualError) {
      console.log('Found individual account:', individualData);
      return { ...individualData, user_type: 'individual' };
    }

    // Check corporate account
    const { data: corporateData, error: corporateError } = await supabase
      .from('corporate_accounts')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (corporateData && !corporateError) {
      console.log('Found corporate account:', corporateData);
      return { ...corporateData, user_type: 'corporate' };
    }

    console.log('No profile found for user:', userId);
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

// Update corporate account details
export async function updateCorporateAccount(userId: string, updates: {
  organization_name?: string;
  office_address?: string;
  contact_person?: string;
  contact_phone?: string;
  business_type?: string;
  registration_number?: string;
  tax_identification_number?: string;
  website?: string;
  industry?: string;
  company_size?: '1-10' | '11-50' | '51-200' | '201-500' | '500+';
  years_in_business?: number;
  annual_revenue?: number;
  bank_name?: string;
  account_number?: string;
  account_name?: string;
  approval_status?: 'pending' | 'approved' | 'rejected';
  verification_status?: 'pending' | 'verified' | 'rejected';
}) {
  try {
    console.log('Updating corporate account for user:', userId, 'with updates:', updates);
    
    const { error } = await supabase
      .from('corporate_accounts')
      .update(updates)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating corporate account:', error);
      return { success: false, error: error.message };
    }

    console.log('Corporate account updated successfully');
    return { success: true };
  } catch (error) {
    console.error('Error updating corporate account:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Update user profile
export async function updateUserProfile(userId: string, updates: Record<string, unknown>) {
  try {
    const profile = await getUserProfile(userId);
    
    if (!profile) {
      return { success: false, error: 'Profile not found' };
    }

    const tableName = profile.user_type === 'individual' ? 'individual_accounts' : 'corporate_accounts';
    
    console.log(`Updating ${tableName} for user ${userId} with updates:`, updates);
    
    const { error } = await supabase
      .from(tableName)
      .update(updates)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating profile:', error);
      return { success: false, error: error.message };
    }

    console.log('Profile updated successfully');
    return { success: true };
  } catch (error) {
    console.error('Error updating profile:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Upload document
export async function uploadDocument(userId: string, file: File, documentType: string) {
  try {
    const fileName = `${userId}/${documentType}_${Date.now()}_${file.name}`;
    
    // Upload to storage
    const { error: uploadError } = await supabase.storage
      .from('documents')
      .upload(fileName, file);

    if (uploadError) {
      return { success: false, error: uploadError.message };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('documents')
      .getPublicUrl(fileName);

    // Save document record
    const { error: dbError } = await supabase
      .from('documents')
      .insert({
        user_id: userId,
        document_type: documentType,
        file_name: fileName,
        file_url: urlData.publicUrl,
        status: 'pending'
      });

    if (dbError) {
      return { success: false, error: dbError.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Document upload error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Get user documents
export async function getUserDocuments(userId: string) {
  try {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, documents: data };
  } catch (error) {
    console.error('Error getting documents:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Send email verification
export async function sendEmailVerification(email: string): Promise<AuthResult> {
  try {
    console.log('Sending email verification for:', email);
    
    // First check if the user exists and needs verification
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.log('No user logged in, checking if user exists by email');
      // If no user is logged in, we need to check if the user exists
      const { data: { users }, error: listError } = await supabase.auth.admin.listUsers();
      
      if (listError) {
        console.error('Error listing users:', listError);
        return { success: false, error: "Please complete signup first" };
      }
      
      const existingUser = users?.find(u => u.email === email);
      if (!existingUser) {
        return { success: false, error: "User not found. Please complete signup first" };
      }
      
      if (existingUser.email_confirmed_at) {
        return { success: false, error: "Email is already verified" };
      }
    } else if (user && user.email_confirmed_at) {
      return { success: false, error: "Email is already verified" };
    }

    // Resend verification email
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: email,
    });

    if (error) {
      console.error('Error sending verification email:', error);
      return { success: false, error: error.message };
    }

    console.log('Verification email sent successfully');
    return { success: true };
  } catch (error) {
    console.error('Email verification error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Verify email with OTP
export async function verifyEmailWithOTP(email: string, token: string): Promise<AuthResult> {
  try {
    console.log('Verifying email with OTP for:', email);
    
    const { data, error } = await supabase.auth.verifyOtp({
      email: email,
      token: token,
      type: 'signup'
    });

    if (error) {
      console.error('OTP verification error:', error);
      return { success: false, error: error.message };
    }

    console.log('Email verified successfully:', data);
    return { success: true, user: data.user };
  } catch (error) {
    console.error('OTP verification error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Verify email with OTP (alternative method)
export async function verifyEmailOTP(email: string, otp: string): Promise<AuthResult> {
  try {
    console.log('Attempting to verify OTP:', { email, otp });
    
    const { data, error } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'signup'
    });

    if (error) {
      console.error('OTP verification error:', error);
      return { success: false, error: error.message };
    }

    console.log('OTP verification successful:', data);
    return { success: true, user: data.user };
  } catch (error) {
    console.error('OTP verification error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Auth state change listener
export function onAuthStateChange(callback: (user: User | null) => void) {
  return supabase.auth.onAuthStateChange((event, session) => {
    callback(session?.user || null);
  });
}

// Test corporate signup integration
export async function testCorporateSignupIntegration() {
  try {
    console.log('Testing corporate signup integration...');
    
    // Test 1: Check if corporate_accounts table exists and is accessible
    const { error: corporateTableError } = await supabase
      .from('corporate_accounts')
      .select('id')
      .limit(1);
    
    if (corporateTableError) {
      console.error('Corporate accounts table error:', corporateTableError);
      return { success: false, error: `Corporate accounts table error: ${corporateTableError.message}` };
    }
    
    console.log('✓ Corporate accounts table accessible');
    
    // Test 2: Check if documents table exists and is accessible
    const { error: documentsTableError } = await supabase
      .from('documents')
      .select('id')
      .limit(1);
    
    if (documentsTableError) {
      console.error('Documents table error:', documentsTableError);
      return { success: false, error: `Documents table error: ${documentsTableError.message}` };
    }
    
    console.log('✓ Documents table accessible');
    
    // Test 3: Check if profiles table exists and is accessible
    const { error: profilesTableError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (profilesTableError) {
      console.error('Profiles table error:', profilesTableError);
      return { success: false, error: `Profiles table error: ${profilesTableError.message}` };
    }
    
    console.log('✓ Profiles table accessible');
    
    // Test 4: Check if storage bucket exists (for document uploads)
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets();
    
    if (storageError) {
      console.error('Storage error:', storageError);
      return { success: false, error: `Storage error: ${storageError.message}` };
    }
    
    const documentsBucket = buckets?.find(bucket => bucket.name === 'documents');
    if (!documentsBucket) {
      console.warn('⚠ Documents storage bucket not found - document uploads may fail');
    } else {
      console.log('✓ Documents storage bucket accessible');
    }
    
    // Test 5: Check RLS policies
    console.log('✓ RLS policies should be in place (checked in migration)');
    
    return { 
      success: true, 
      message: 'Corporate signup integration test passed',
      details: {
        tables: ['corporate_accounts', 'documents', 'profiles'],
        storage: documentsBucket ? 'available' : 'missing',
        rls: 'enabled'
      }
    };
  } catch (error) {
    console.error('Corporate signup integration test error:', error);
    return { success: false, error: 'Corporate signup integration test failed' };
  }
}

// Admin functions for managing accounts
export async function getAllIndividualAccounts() {
  try {
    console.log('Fetching all individual accounts...');
    
    const { data, error } = await supabase
      .from('individual_accounts')
      .select(`
        id,
        user_id,
        email,
        full_name,
        phone_number,
        bvn,
        verification_status,
        employer,
        position,
        monthly_income,
        employment_type,
        is_active,
        created_at,
        updated_at
      `);

    if (error) {
      console.error('Error fetching individual accounts:', error);
      return { success: false, error: error.message };
    }

    console.log(`Found ${data?.length || 0} individual accounts`);
    return { success: true, accounts: data };
  } catch (error: unknown) {
    return { success: false, error: (error as Error).message };
  }
}

export async function getAllCorporateAccounts() {
  try {
    console.log('Fetching all corporate accounts...');
    
    const { data, error } = await supabase
      .from('corporate_accounts')
      .select(`
        id,
        user_id,
        email,
        organization_name,
        office_address,
        contact_person,
        contact_phone,
        business_type,
        industry,
        company_size,
        approval_status,
        verification_status,
        is_active,
        created_at,
        updated_at
      `);

    if (error) {
      console.error('Error fetching corporate accounts:', error);
      return { success: false, error: error.message };
    }

    console.log(`Found ${data?.length || 0} corporate accounts`);
    return { success: true, accounts: data };
  } catch (error: unknown) {
    return { success: false, error: (error as Error).message };
  }
}

export async function getAllDocuments() {
  try {
    console.log('Fetching all documents...');
    
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching documents:', error);
      return { success: false, error: error.message };
    }

    console.log(`Found ${data?.length || 0} documents`);
    return { success: true, documents: data };
  } catch (error) {
    console.error('Error fetching documents:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function updateIndividualAccountStatus(userId: string, status: 'pending' | 'verified' | 'rejected') {
  try {
    console.log(`Updating individual account status for user ${userId} to ${status}`);
    
    const { error } = await supabase
      .from('individual_accounts')
      .update({ verification_status: status })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating individual account status:', error);
      return { success: false, error: error.message };
    }

    console.log('Individual account status updated successfully');
    return { success: true };
  } catch (error) {
    console.error('Error updating individual account status:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function updateCorporateAccountStatus(userId: string, approvalStatus: 'pending' | 'approved' | 'rejected', verificationStatus?: 'pending' | 'verified' | 'rejected') {
  try {
    console.log(`Updating corporate account status for user ${userId}: approval=${approvalStatus}, verification=${verificationStatus}`);
    
    const updates: {
      approval_status: 'pending' | 'approved' | 'rejected';
      verification_status?: 'pending' | 'verified' | 'rejected';
    } = { approval_status: approvalStatus };
    
    if (verificationStatus) {
      updates.verification_status = verificationStatus;
    }
    
    const { error } = await supabase
      .from('corporate_accounts')
      .update(updates)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating corporate account status:', error);
      return { success: false, error: error.message };
    }

    console.log('Corporate account status updated successfully');
    return { success: true };
  } catch (error) {
    console.error('Error updating corporate account status:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function updateDocumentStatus(documentId: string, status: 'pending' | 'uploaded' | 'verified' | 'rejected') {
  try {
    console.log(`Updating document status for document ${documentId} to ${status}`);
    
    const { error } = await supabase
      .from('documents')
      .update({ status })
      .eq('id', documentId);

    if (error) {
      console.error('Error updating document status:', error);
      return { success: false, error: error.message };
    }

    console.log('Document status updated successfully');
    return { success: true };
  } catch (error) {
    console.error('Error updating document status:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function deleteAccount(userId: string, accountType: 'individual' | 'corporate') {
  try {
    console.log(`Deleting ${accountType} account for user ${userId}`);
    
    const tableName = accountType === 'individual' ? 'individual_accounts' : 'corporate_accounts';
    
    const { error } = await supabase
      .from(tableName)
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error(`Error deleting ${accountType} account:`, error);
      return { success: false, error: error.message };
    }

    console.log(`${accountType} account deleted successfully`);
    return { success: true };
  } catch (error) {
    console.error(`Error deleting ${accountType} account:`, error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Admin authentication and management functions
export async function checkAdminStatus(userId: string) {
  try {
    console.log('Checking admin status for user:', userId);
    
    const { data, error } = await supabase
      .from('profiles')
      .select('is_admin, admin_level')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking admin status:', error);
      return { success: false, error: error.message };
    }

    const isAdmin = data?.is_admin || false;
    const adminLevel = data?.admin_level || null;

    console.log('Admin status:', { isAdmin, adminLevel });
    return { 
      success: true, 
      isAdmin, 
      adminLevel 
    };
  } catch (error) {
    console.error('Error checking admin status:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function createAdminUser(adminEmail: string, adminLevel: 'super_admin' | 'admin' | 'moderator' = 'admin') {
  try {
    console.log(`Creating admin user: ${adminEmail} with level: ${adminLevel}`);
    
    const { error } = await supabase.rpc('create_admin_user', {
      admin_email: adminEmail,
      admin_level: adminLevel
    });

    if (error) {
      console.error('Error creating admin user:', error);
      return { success: false, error: error.message };
    }

    console.log('Admin user created successfully');
    return { success: true };
  } catch (error) {
    console.error('Error creating admin user:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function removeAdminUser(adminEmail: string) {
  try {
    console.log(`Removing admin user: ${adminEmail}`);
    
    const { error } = await supabase.rpc('remove_admin_user', {
      admin_email: adminEmail
    });

    if (error) {
      console.error('Error removing admin user:', error);
      return { success: false, error: error.message };
    }

    console.log('Admin user removed successfully');
    return { success: true };
  } catch (error) {
    console.error('Error removing admin user:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function getAllAdminUsers() {
  try {
    console.log('Fetching all admin users...');
    
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching admin users:', error);
      return { success: false, error: error.message };
    }

    console.log(`Found ${data?.length || 0} admin users`);
    return { success: true, adminUsers: data };
  } catch (error) {
    console.error('Error fetching admin users:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Function to manually set admin status (for initial setup)
export async function setAdminStatus(userId: string, isAdmin: boolean, adminLevel?: 'super_admin' | 'admin' | 'moderator') {
  try {
    console.log(`Setting admin status for user ${userId}: isAdmin=${isAdmin}, level=${adminLevel}`);
    
    const updates: {
      is_admin: boolean;
      admin_level?: 'super_admin' | 'admin' | 'moderator' | null;
    } = { is_admin: isAdmin };
    
    if (isAdmin && adminLevel) {
      updates.admin_level = adminLevel;
    } else if (!isAdmin) {
      updates.admin_level = null;
    }
    
    const { error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId);

    if (error) {
      console.error('Error setting admin status:', error);
      return { success: false, error: error.message };
    }

    console.log('Admin status updated successfully');
    return { success: true };
  } catch (error) {
    console.error('Error setting admin status:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function setIndividualAccountActive(userId: string, isActive: boolean) {
    const { error } = await supabase.rpc('set_individual_account_active', {
        p_user_id: userId,
        p_is_active: isActive,
    });

    if (error) {
        console.error('Error updating individual account active status:', error);
        return { success: false, error: error.message };
    }
    return { success: true };
}

export async function setCorporateAccountActive(userId: string, isActive: boolean) {
    const { error } = await supabase.rpc('set_corporate_account_active', {
        p_user_id: userId,
        p_is_active: isActive,
    });

    if (error) {
        console.error('Error updating corporate account active status:', error);
        return { success: false, error: error.message };
    }
    return { success: true };
} 