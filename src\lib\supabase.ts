import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          user_type: 'individual' | 'corporate'
          mode: 'borrower' | 'lender'
          is_admin: boolean
          admin_level: 'super_admin' | 'admin' | 'moderator' | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          user_type: 'individual' | 'corporate'
          mode?: 'borrower' | 'lender'
          is_admin?: boolean
          admin_level?: 'super_admin' | 'admin' | 'moderator' | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          user_type?: 'individual' | 'corporate'
          mode?: 'borrower' | 'lender'
          is_admin?: boolean
          admin_level?: 'super_admin' | 'admin' | 'moderator' | null
          created_at?: string
          updated_at?: string
        }
      }
      individual_accounts: {
        Row: {
          id: string
          user_id: string
          email: string
          bvn: string | null
          date_of_birth: string | null
          full_name: string | null
          phone_number: string | null
          verification_status: 'pending' | 'verified' | 'rejected'
          employer: string | null
          position: string | null
          monthly_income: number | null
          employment_type: 'full-time' | 'part-time' | 'self-employed' | 'unemployed' | null
          guarantor_name: string | null
          guarantor_relationship: string | null
          guarantor_phone: string | null
          guarantor_email: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          bvn?: string | null
          date_of_birth?: string | null
          full_name?: string | null
          phone_number?: string | null
          verification_status?: 'pending' | 'verified' | 'rejected'
          employer?: string | null
          position?: string | null
          monthly_income?: number | null
          employment_type?: 'full-time' | 'part-time' | 'self-employed' | 'unemployed' | null
          guarantor_name?: string | null
          guarantor_relationship?: string | null
          guarantor_phone?: string | null
          guarantor_email?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          bvn?: string | null
          date_of_birth?: string | null
          full_name?: string | null
          phone_number?: string | null
          verification_status?: 'pending' | 'verified' | 'rejected'
          employer?: string | null
          position?: string | null
          monthly_income?: number | null
          employment_type?: 'full-time' | 'part-time' | 'self-employed' | 'unemployed' | null
          guarantor_name?: string | null
          guarantor_relationship?: string | null
          guarantor_phone?: string | null
          guarantor_email?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      corporate_accounts: {
        Row: {
          id: string
          user_id: string
          email: string
          organization_name: string | null
          office_address: string | null
          approval_status: 'pending' | 'approved' | 'rejected'
          verification_status: 'pending' | 'verified' | 'rejected'
          contact_person: string | null
          contact_phone: string | null
          business_type: string | null
          registration_number: string | null
          tax_identification_number: string | null
          website: string | null
          industry: string | null
          company_size: '1-10' | '11-50' | '51-200' | '201-500' | '500+' | null
          years_in_business: number | null
          annual_revenue: number | null
          bank_name: string | null
          account_number: string | null
          account_name: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          organization_name?: string | null
          office_address?: string | null
          approval_status?: 'pending' | 'approved' | 'rejected'
          verification_status?: 'pending' | 'verified' | 'rejected'
          contact_person?: string | null
          contact_phone?: string | null
          business_type?: string | null
          registration_number?: string | null
          tax_identification_number?: string | null
          website?: string | null
          industry?: string | null
          company_size?: '1-10' | '11-50' | '51-200' | '201-500' | '500+' | null
          years_in_business?: number | null
          annual_revenue?: number | null
          bank_name?: string | null
          account_number?: string | null
          account_name?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          organization_name?: string | null
          office_address?: string | null
          approval_status?: 'pending' | 'approved' | 'rejected'
          verification_status?: 'pending' | 'verified' | 'rejected'
          contact_person?: string | null
          contact_phone?: string | null
          business_type?: string | null
          registration_number?: string | null
          tax_identification_number?: string | null
          website?: string | null
          industry?: string | null
          company_size?: '1-10' | '11-50' | '51-200' | '201-500' | '500+' | null
          years_in_business?: number | null
          annual_revenue?: number | null
          bank_name?: string | null
          account_number?: string | null
          account_name?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      documents: {
        Row: {
          id: string
          user_id: string
          document_type: string
          file_name: string
          file_url: string | null
          status: 'pending' | 'uploaded' | 'verified' | 'rejected'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          document_type: string
          file_name: string
          file_url?: string | null
          status?: 'pending' | 'uploaded' | 'verified' | 'rejected'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          document_type?: string
          file_name?: string
          file_url?: string | null
          status?: 'pending' | 'uploaded' | 'verified' | 'rejected'
          created_at?: string
          updated_at?: string
        }
      }
      admin_users: {
        Row: {
          id: string
          user_id: string
          email: string
          admin_level: 'super_admin' | 'admin' | 'moderator'
          permissions: Record<string, unknown>
          is_active: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          admin_level: 'super_admin' | 'admin' | 'moderator'
          permissions?: Record<string, unknown>
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          admin_level?: 'super_admin' | 'admin' | 'moderator'
          permissions?: Record<string, unknown>
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
} 